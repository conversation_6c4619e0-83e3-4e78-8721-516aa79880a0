"use client";

import {
    ColumnDef,
    flexRender,
    getCoreRowModel,
    useReactTable,
    SortingState,
    getSortedRowModel,
    getFilteredRowModel,
    getPaginationRowModel,
    VisibilityState,
    ColumnFiltersState,
} from "@tanstack/react-table";
import { useState } from "react";

import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";

import PageLoading from "@/components/loading/PageLoading";
import React from "react";
import { DataTablePagination } from "./data-table-pagination";

// Define a type for the status
export type DataTableStatus = "loading" | "error" | "success" | "empty";

interface DataTableProps<TData, TValue> {
    columns: ColumnDef<TData, TValue>[];
    data: TData[];
    status?: DataTableStatus;
    filterValue?: string;
}

export function DataTable<TData, TValue>({
    columns,
    data,
    status = "success",
    filterValue = "",
}: DataTableProps<TData, TValue>) {
    const [sorting, setSorting] = useState<SortingState>([]);
    const [columnFilters, setColumnFilters] =
        React.useState<ColumnFiltersState>([]);
    const [columnVisibility, setColumnVisibility] =
        React.useState<VisibilityState>({});
    const [globalFilter, setGlobalFilter] = useState(filterValue);

    const table = useReactTable({
        data,
        columns,
        getCoreRowModel: getCoreRowModel(),
        getSortedRowModel: getSortedRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        onSortingChange: setSorting,
        onColumnFiltersChange: setColumnFilters,
        getFilteredRowModel: getFilteredRowModel(),
        onColumnVisibilityChange: setColumnVisibility,
        globalFilterFn: "includesString",
        onGlobalFilterChange: setGlobalFilter,
        state: {
            sorting,
            columnFilters,
            columnVisibility,
            globalFilter: filterValue,
        },
    });

    // Render loading state
    if (status === "loading") {
        return (
            <div className="space-y-4 w-full">
                <div className="rounded-md shadow-sm border border-gray-200 overflow-hidden">
                    <div className="overflow-x-auto">
                        <Table className="w-full border-collapse min-w-[600px]">
                            <TableHeader>
                                {columns.length > 0 && (
                                    <TableRow className="bg-gray-50">
                                        {columns.map((column, index) => (
                                            <TableHead
                                                key={index}
                                                className="font-semibold py-2 whitespace-nowrap"
                                            >
                                                {typeof column.header ===
                                                "string"
                                                    ? column.header
                                                    : column.id}
                                            </TableHead>
                                        ))}
                                    </TableRow>
                                )}
                            </TableHeader>
                            <TableBody>
                                <TableRow>
                                    <TableCell
                                        colSpan={columns.length}
                                        className="h-64 text-center"
                                    >
                                        <PageLoading />
                                    </TableCell>
                                </TableRow>
                            </TableBody>
                        </Table>
                    </div>
                </div>
            </div>
        );
    }

    // Render error state
    if (status === "error") {
        return (
            <div className="space-y-4 w-full">
                <div className="rounded-md shadow-sm border border-gray-200 overflow-hidden">
                    <div className="overflow-x-auto">
                        <Table className="w-full border-collapse min-w-[600px]">
                            <TableHeader>
                                {columns.length > 0 && (
                                    <TableRow className="bg-gray-50">
                                        {columns.map((column, index) => (
                                            <TableHead
                                                key={index}
                                                className="font-semibold py-2 whitespace-nowrap"
                                            >
                                                {typeof column.header ===
                                                "string"
                                                    ? column.header
                                                    : column.id}
                                            </TableHead>
                                        ))}
                                    </TableRow>
                                )}
                            </TableHeader>
                            <TableBody>
                                <TableRow>
                                    <TableCell
                                        colSpan={columns.length}
                                        className="h-24 text-center text-red-500"
                                    >
                                        An error occurred while loading data.
                                    </TableCell>
                                </TableRow>
                            </TableBody>
                        </Table>
                    </div>
                </div>
            </div>
        );
    }

    // Render empty state or data
    return (
        <div className="space-y-4 w-full">
            <div className="rounded-md shadow-sm border border-gray-200 overflow-hidden">
                <div className="overflow-x-auto">
                    <Table className="w-full border-collapse min-w-[600px]">
                        <TableHeader>
                            {table.getHeaderGroups().map((headerGroup) => (
                                <TableRow
                                    key={headerGroup.id}
                                    className="bg-gray-50"
                                >
                                    {headerGroup.headers.map((header) => (
                                        <TableHead
                                            key={header.id}
                                            className="font-semibold py-2 whitespace-nowrap"
                                            style={{
                                                width: `${header.getSize()}px`,
                                            }}
                                        >
                                            {header.isPlaceholder
                                                ? null
                                                : flexRender(
                                                      header.column.columnDef
                                                          .header,
                                                      header.getContext(),
                                                  )}
                                        </TableHead>
                                    ))}
                                </TableRow>
                            ))}
                        </TableHeader>
                        <TableBody>
                            {table.getRowModel().rows?.length ? (
                                table.getRowModel().rows.map((row) => (
                                    <TableRow
                                        key={row.id}
                                        data-state={
                                            row.getIsSelected() && "selected"
                                        }
                                        className="border-t border-gray-200"
                                    >
                                        {row.getVisibleCells().map((cell) => (
                                            <TableCell
                                                key={cell.id}
                                                className="whitespace-nowrap"
                                            >
                                                {flexRender(
                                                    cell.column.columnDef.cell,
                                                    cell.getContext(),
                                                )}
                                            </TableCell>
                                        ))}
                                    </TableRow>
                                ))
                            ) : (
                                <TableRow>
                                    <TableCell
                                        colSpan={columns.length}
                                        className="h-24 text-center"
                                    >
                                        {"No results found."}
                                    </TableCell>
                                </TableRow>
                            )}
                        </TableBody>
                    </Table>
                </div>
            </div>
            <DataTablePagination table={table} />
        </div>
    );
}
