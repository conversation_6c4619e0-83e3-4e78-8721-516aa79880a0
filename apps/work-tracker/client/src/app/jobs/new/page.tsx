"use client";

import { useRouter } from "next/navigation";
import { Loader2 } from "lucide-react";

import JobsForm from "@/features/jobs/components/form/JobsForm";
import useTitle from "@/hooks/useTitle";
import { JobFormData } from "@/features/jobs/lib/validations";
import { useJobCrud } from "@/features/jobs/hooks/useJobs";
import { devices, parts } from "@/zero/generatedSchema";
import { useParts } from "@/hooks/useParts";
import { useDevices } from "@/hooks/useDevices";

export default function NewJobPage() {
    useTitle("Create New Job");
    const router = useRouter();
    const { createJob, isLoading } = useJobCrud();

    // Fetch parts and devices data
    const { parts, status: partsStatus } = useParts();
    const { devices, status: devicesStatus } = useDevices();

    // Handle form submission
    const handleSubmit = async (data: JobFormData) => {
        try {
            await createJob(data);
        } catch (error) {
            // Error is already handled in the hook
        }
    };

    // Handle cancel button
    const handleCancel = () => {
        router.push("/jobs");
    };

    // Show loading state if data is still loading
    if (partsStatus.type === "unknown" || devicesStatus.type === "unknown") {
        return (
            <div className="flex items-center justify-center h-screen">
                <Loader2 className="h-8 w-8 animate-spin" />
                <span className="ml-2">Loading data...</span>
            </div>
        );
    }

    return (
        <main className="w-full max-w-6xl p-8 mx-auto flex flex-col">
            <h1 className="font-semibold text-3xl pb-12">Create New Job</h1>
            <div className="w-full max-w-6xl ">
                <JobsForm
                    availableParts={parts as unknown as parts[]}
                    availableDevices={devices as unknown as devices[]}
                    onSubmit={handleSubmit}
                    isSubmitting={isLoading}
                    onCancel={handleCancel}
                />
            </div>
        </main>
    );
}
