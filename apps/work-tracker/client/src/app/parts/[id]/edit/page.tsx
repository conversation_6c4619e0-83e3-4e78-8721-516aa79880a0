"use client";

import { useEffect, useState } from "react";
import { useParams, useRouter } from "next/navigation";
import { Loader2 } from "lucide-react";

import PartsForm from "@/features/parts/components/form/PartsForm";
import useTitle from "@/hooks/useTitle";
import { PartFormData } from "@/features/parts/lib/validations";
import { usePartsCrud } from "@/features/parts/hooks/useParts";
import { part_configs } from "@/zero/generatedSchema";
import { useParts } from "@/hooks/useParts";

export default function PartEditPage() {
    useTitle("Edit Part");
    const params = useParams();
    const router = useRouter();
    const { updatePart, isLoading } = usePartsCrud();
    const [partId, setPartId] = useState<number | null>(null);
    const [initialValues, setInitialValues] = useState<
        PartFormData | undefined
    >(undefined);

    // Parse part ID from URL params
    useEffect(() => {
        if (params?.id) {
            const id = Number(params.id);
            if (!isNaN(id)) {
                setPartId(id);
            } else {
                console.error("Invalid part ID:", params.id);
            }
        }
    }, [params, router]);

    // Fetch part data with configs
    const { part, status: partStatus } = useParts().getPartById(partId);

    // Set initial values when part data is loaded
    useEffect(() => {
        if (part && partStatus.type === "complete") {
            const configs = (part.configs || []).map((config: any) => ({
                id: config.id,
                deviceType: config.device_type,
                deviceId: config.device_id,
                idealCycleTime: config.ideal_cycle_time,
                taktTime: config.takt_time,
                countMultiplier1: config.count_multiplier1,
                countMultiplier2: config.count_multiplier2,
                countMultiplier3: config.count_multiplier3,
                countMultiplier4: config.count_multiplier4,
                countMultiplier5: config.count_multiplier5,
                countMultiplier6: config.count_multiplier6,
                countMultiplier7: config.count_multiplier7,
                countMultiplier8: config.count_multiplier8,
                targetMultiplier: config.target_multiplier,
                targetLaborPerPiece: config.target_labor_per_piece,
                downThreshold: config.down_threshold,
                startWithChangeover: config.start_with_changeover,
                changeoverTarget: config.changeover_target,
                changeoverReason: config.changeover_reason || "",
                disableWhen: config.disable_when || "running",
            }));

            setInitialValues({
                name: part.name,
                displayName: part.display_name || "",
                configs: configs,
            });
        }
    }, [part, partStatus]);

    // Handle form submission
    const handleSubmit = async (data: PartFormData) => {
        if (!partId) return;

        try {
            await updatePart(
                partId,
                data,
                part?.configs as unknown as part_configs[],
            );
        } catch (error) {
            // Error is already handled in the hook
        }
    };

    // Handle cancel button
    const handleCancel = () => {
        router.push("/parts");
    };

    // Show loading state if part data is still loading
    if (partStatus.type === "unknown") {
        return (
            <div className="flex items-center justify-center h-screen">
                <Loader2 className="h-8 w-8 animate-spin" />
                <span className="ml-2">Loading part...</span>
            </div>
        );
    }

    // Show error state if part not found
    if (partStatus.type === "complete" && !part) {
        return (
            <div className="flex items-center justify-center h-screen">
                <div className="text-center">
                    <h2 className="text-2xl font-semibold mb-2">
                        Part Not Found
                    </h2>
                    <p className="text-gray-600 mb-4">
                        The part you're looking for doesn't exist.
                    </p>
                    <button
                        onClick={() => router.push("/parts")}
                        className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                    >
                        Back to Parts
                    </button>
                </div>
            </div>
        );
    }

    return (
        <main className="w-full max-w-6xl p-8 mx-auto flex flex-col">
            <h1 className="font-semibold text-3xl pb-12">Edit Part</h1>
            <div className="w-full max-w-6xl">
                {initialValues && (
                    <PartsForm
                        initialValues={initialValues}
                        onSubmit={handleSubmit}
                        isSubmitting={isLoading}
                        onCancel={handleCancel}
                    />
                )}
            </div>
        </main>
    );
}
