"use client";

import { useQuery, useZero } from "@rocicorp/zero/react";

/**
 * Hook to get parts data using <PERSON>'s useQuery
 */
export const useParts = () => {
    const z = useZero();

    // Get all parts with their configs
    const [parts, status] = useQuery(
        z.query.parts.related("configs").orderBy("inserted_date", "desc"),
    );

    // Get a specific part by ID
    const getPartById = (partId: number | null) => {
        const [part, status] = useQuery(
            z.query.parts.where("id", "=", partId).related("configs").one(),
        );

        return { part, status };
    };

    return {
        parts,
        status,
        getPartById,
    };
};
