"use client";

import { useQuery, useZero } from "@rocicorp/zero/react";

/**
 * Hook to get parts data using Zero's useQuery
 */
export const useJobs = () => {
    const z = useZero();

    const [jobs, status] = useQuery(
        z.query.jobs.related("configs").orderBy("inserted_date", "desc"),
    );

    const [allJobs, allJobsStatus] = useQuery(z.query.jobs.related("configs"));

    const getJobById = (jobId: number | null) => {
        const [job, status] = useQuery(
            z.query.jobs.where("id", "=", jobId).related("configs").one(),
        );

        return { job, status };
    };

    // Pre-query jobs by part ID at hook initialization time
    const getJobsByPartId = (partId: number | null) => {
        // Filter jobs from the already queried allJobs
        const filteredJobs =
            allJobs?.filter((job) => job.part_id === partId) || [];
        return { jobs: filteredJobs, status: allJobsStatus };
    };

    return {
        jobs,
        status,
        getJobById,
        getJobsByPartId,
    };
};
