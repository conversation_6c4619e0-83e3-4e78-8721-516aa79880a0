import { useState } from "react";
import { toast } from "sonner";
import { useZero } from "@rocicorp/zero/react";
import { job_configs, jobs } from "@/zero/generatedSchema";
import { JobFormData } from "@/features/jobs/lib/validations";
import { useRouter } from "next/navigation";
import { useNextId } from "@/hooks/useNextId";

// Define an extended type that includes the configs relationship
export interface JobWithConfigs extends jobs {
    configs?: job_configs[];
}

export const useJobCrud = () => {
    const [isLoading, setIsLoading] = useState(false);
    const z = useZero();
    const router = useRouter();
    const { getNextId: getNextJobId } = useNextId("jobs");
    const { getNextId: getNextJobConfigId } = useNextId("job_configs");

    // Create a new job
    const createJob = async (data: JobFormData) => {
        setIsLoading(true);
        try {
            // Get the latest job ID and increment by 1
            const newJobId = getNextJobId() + 1;

            // Create job using Zero mutation
            await z.mutate.jobs.insert({
                id: newJobId,
                part_id: data.partId,
                name: data.name,
                display_name: data.displayName || data.name,
                rework: data.rework,
                is_active: data.isActive,
                inserted_date: Date.now(),
                inserted_by: "Current User", // TODO: Get from auth context
            });

            // Insert configs if any
            if (data.configs && data.configs.length > 0) {
                await upsertJobConfigs(newJobId, data.configs);
            }

            toast.success("Job created successfully!");
            return newJobId;
        } catch (error) {
            console.error("Failed to create job:", error);
            toast.error(
                `Failed to create job: ${error instanceof Error ? error.message : "Unknown error"}`,
            );
            throw error;
        } finally {
            router.push("/jobs");
            setIsLoading(false);
        }
    };

    // Update an existing job
    const updateJob = async (
        id: number,
        data: JobFormData,
        existingConfigs?: job_configs[],
    ) => {
        setIsLoading(true);
        try {
            // Update job using Zero mutation
            await z.mutate.jobs.update({
                id: id,
                part_id: data.partId,
                name: data.name,
                display_name: data.displayName || data.name,
                rework: data.rework,
                is_active: data.isActive,
            });

            // Handle configs update with the new method
            if (existingConfigs) {
                await upsertJobConfigs(id, data.configs, existingConfigs);
            }

            toast.success("Job updated successfully!");
            return id;
        } catch (error) {
            console.error("Failed to update job:", error);
            toast.error(
                `Failed to update job: ${error instanceof Error ? error.message : "Unknown error"}`,
            );
            throw error;
        } finally {
            router.push("/jobs");
            setIsLoading(false);
        }
    };

    // Helper function to insert/update job configs
    const upsertJobConfigs = async (
        jobId: number,
        newConfigs: JobFormData["configs"],
        existingConfigs: job_configs[] = [],
    ) => {
        if (!newConfigs) return;

        // Create maps for existing configs
        const existingConfigsById = new Map(
            existingConfigs.map((config) => [config.id, config]),
        );

        // Track processed configs
        const processedConfigIds = new Set<number>();

        // Process each new config
        for (const newConfig of newConfigs) {
            if (newConfig.id) {
                // Case 1: Config with ID exists - check for updates
                const existingConfig = existingConfigsById.get(newConfig.id);

                if (existingConfig) {
                    // Check if any values changed
                    if (
                        existingConfig.goal_count !== newConfig.goalCount ||
                        existingConfig.expected_duration !==
                            newConfig.expectedDuration ||
                        existingConfig.device_type !== newConfig.deviceType ||
                        existingConfig.device_id !== newConfig.deviceId
                    ) {
                        await z.mutate.job_configs.update({
                            id: existingConfig.id,
                            device_type: newConfig.deviceType,
                            device_id: newConfig.deviceId,
                            goal_count: newConfig.goalCount,
                            expected_duration: newConfig.expectedDuration,
                        });
                    }
                    processedConfigIds.add(existingConfig.id);
                }
            } else {
                // Case 2: New config without ID - create it
                const newConfigId = getNextJobConfigId();

                await z.mutate.job_configs.insert({
                    id: newConfigId,
                    job_id: jobId,
                    device_type: newConfig.deviceType,
                    device_id: newConfig.deviceId,
                    goal_count: newConfig.goalCount,
                    expected_duration: newConfig.expectedDuration,
                    is_active: true,
                    inserted_date: Date.now(),
                    inserted_by: "Current User",
                });

                processedConfigIds.add(newConfigId);
            }
        }

        // Delete configs that weren't processed (no longer in newConfigs)
        for (const existingConfig of existingConfigs) {
            if (!processedConfigIds.has(existingConfig.id)) {
                await z.mutate.job_configs.delete({ id: existingConfig.id });
            }
        }
    };

    // Delete a job
    const deleteJob = async (job: JobWithConfigs) => {
        setIsLoading(true);
        try {
            // First, delete all associated job configs
            if (job.configs && job.configs.length > 0) {
                for (const config of job.configs) {
                    await z.mutate.job_configs.delete({
                        id: config.id,
                    });
                }
            }

            // Then delete the job itself
            await z.mutate.jobs.delete({
                id: job.id,
            });
            toast.success(`Successfully deleted ${job.name}`);
            return true;
        } catch (error) {
            console.error("Failed to delete job:", error);
            toast.error(
                `Failed to delete job: ${error instanceof Error ? error.message : "Unknown error"}`,
            );
            throw error;
        } finally {
            setIsLoading(false);
        }
    };

    // Toggle job active status
    const toggleJobStatus = async (job: jobs) => {
        try {
            await z.mutate.jobs.update({
                id: job.id,
                is_active: !job.is_active,
            });

            toast.success(
                `Successfully ${job.is_active ? "deactivated" : "activated"} ${job.name}`,
            );
            return true;
        } catch (error) {
            console.error("Error updating job status:", error);
            toast.error(
                `Failed to ${job.is_active ? "deactivate" : "activate"} ${job.name}`,
            );
            throw error;
        }
    };

    // Navigate to job details
    const viewJobDetails = (jobId: number) => {
        router.push(`/jobs/${jobId}/edit`);
    };

    return {
        createJob,
        updateJob,
        deleteJob,
        toggleJobStatus,
        viewJobDetails,
        isLoading,
    };
};
