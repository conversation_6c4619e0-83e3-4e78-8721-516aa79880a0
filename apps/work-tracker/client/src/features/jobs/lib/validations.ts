import { z } from "zod";

// Job form validation schema based on the Job interface
export const jobFormSchema = z.object({
    // Basic job information
    partId: z.coerce
        .number({
            required_error: "Part ID is required",
            invalid_type_error: "Part ID must be a number",
        })
        .min(1, { message: "Please select a valid part" }),

    name: z
        .string({ required_error: "Job name is required" })
        .min(1, { message: "Job name is required" })
        .max(100, { message: "Job name must be less than 100 characters" }),

    displayName: z
        .string()
        .max(100, { message: "Display name must be less than 100 characters" })
        .optional(),

    rework: z.boolean(),
    isActive: z.boolean(),

    // Job configuration (optional)
    configs: z.array(
        z.object({
            id: z.number().optional(),
            deviceType: z
                .string()
                .min(1, { message: "Device type is required" }),
            deviceId: z.coerce
                .number({ required_error: "Device is required" })
                .min(1, { message: "Please select a device" }),
            goalCount: z.coerce
                .number({ message: "Goal count must be a number" })
                .min(1, { message: "Goal count must be at least 1" })
                .max(10000, {
                    message: "Goal count must be less than 10,000",
                }),
            expectedDuration: z.coerce
                .number({ message: "Expected duration must be a number" })
                .min(1, {
                    message: "Expected duration must be at least 1 minute",
                })
                .max(1440, {
                    message: "Expected duration must be less than 24 hours",
                })
                .optional(),
        }),
    ),
});

export type JobFormData = z.infer<typeof jobFormSchema>;
