"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useZero } from "@rocicorp/zero/react";
import { toast } from "sonner";

import { PartFormData, PartConfigData } from "@/features/parts/lib/validations";
import { parts, part_configs } from "@/zero/generatedSchema";
import { useNextId } from "@/hooks/useNextId";
import { useJobs } from "@/hooks/useJobs";

export const usePartsCrud = () => {
    const [isLoading, setIsLoading] = useState(false);
    const router = useRouter();
    const z = useZero();
    const { getJobsByPartId } = useJobs();
    const { getNextId: getNextPartId } = useNextId("parts");
    const { getNextId: getNextPartConfigId } = useNextId("part_configs");

    // Helper function to insert/update part configs
    const upsertPartConfigs = async (
        partId: number,
        newConfigs: PartConfigData[],
        existingConfigs: part_configs[] = [],
    ) => {
        if (!newConfigs) return;

        // Create maps for existing configs
        const existingConfigsById = new Map(
            existingConfigs.map((config) => [config.id, config]),
        );

        // Track processed configs
        const processedConfigIds = new Set<number>();

        // Process each new config
        for (const newConfig of newConfigs) {
            if (newConfig.id) {
                // Case 1: Config with ID exists - check for updates
                const existingConfig = existingConfigsById.get(newConfig.id);

                if (existingConfig) {
                    // Check if any values changed
                    if (
                        existingConfig.device_type !== newConfig.deviceType ||
                        existingConfig.device_id !== newConfig.deviceId ||
                        existingConfig.ideal_cycle_time !==
                            newConfig.idealCycleTime ||
                        existingConfig.takt_time !== newConfig.taktTime ||
                        // Add all other field comparisons
                        existingConfig.changeover_target !==
                            newConfig.changeoverTarget ||
                        existingConfig.changeover_reason !==
                            (newConfig.changeoverReason || "") ||
                        existingConfig.disable_when !==
                            (newConfig.disableWhen || "")
                        // Include all other config fields
                    ) {
                        await z.mutate.part_configs.update({
                            id: existingConfig.id,
                            // Map form fields to database columns
                            device_type: newConfig.deviceType,
                            device_id: newConfig.deviceId,
                            ideal_cycle_time: newConfig.idealCycleTime,
                            takt_time: newConfig.taktTime,
                            count_multiplier1: newConfig.countMultiplier1,
                            count_multiplier2: newConfig.countMultiplier2,
                            count_multiplier3: newConfig.countMultiplier3,
                            count_multiplier4: newConfig.countMultiplier4,
                            count_multiplier5: newConfig.countMultiplier5,
                            count_multiplier6: newConfig.countMultiplier6,
                            count_multiplier7: newConfig.countMultiplier7,
                            count_multiplier8: newConfig.countMultiplier8,
                            target_multiplier: newConfig.targetMultiplier,
                            target_labor_per_piece:
                                newConfig.targetLaborPerPiece,
                            down_threshold: newConfig.downThreshold,
                            start_with_changeover:
                                newConfig.startWithChangeover,
                            changeover_target: newConfig.changeoverTarget,
                            changeover_reason: newConfig.changeoverReason || "",
                            disable_when: newConfig.disableWhen || "",
                        });
                    }
                    processedConfigIds.add(existingConfig.id);
                }
            } else {
                // Case 2: New config without ID - create it
                const newConfigId = getNextPartConfigId();

                await z.mutate.part_configs.insert({
                    id: newConfigId,
                    part_id: partId,
                    // Map form fields to database columns
                    device_type: newConfig.deviceType,
                    device_id: newConfig.deviceId,
                    ideal_cycle_time: newConfig.idealCycleTime,
                    takt_time: newConfig.taktTime,
                    count_multiplier1: newConfig.countMultiplier1,
                    count_multiplier2: newConfig.countMultiplier2,
                    count_multiplier3: newConfig.countMultiplier3,
                    count_multiplier4: newConfig.countMultiplier4,
                    count_multiplier5: newConfig.countMultiplier5,
                    count_multiplier6: newConfig.countMultiplier6,
                    count_multiplier7: newConfig.countMultiplier7,
                    count_multiplier8: newConfig.countMultiplier8,
                    target_multiplier: newConfig.targetMultiplier,
                    target_labor_per_piece: newConfig.targetLaborPerPiece,
                    down_threshold: newConfig.downThreshold,
                    start_with_changeover: newConfig.startWithChangeover,
                    changeover_target: newConfig.changeoverTarget,
                    changeover_reason: newConfig.changeoverReason || "",
                    disable_when: newConfig.disableWhen || "",
                    is_active: true,
                    inserted_date: Date.now(),
                    inserted_by: "Current User",
                });

                processedConfigIds.add(newConfigId);
            }
        }

        // Delete configs that weren't processed (no longer in newConfigs)
        for (const existingConfig of existingConfigs) {
            if (!processedConfigIds.has(existingConfig.id)) {
                await z.mutate.part_configs.delete({ id: existingConfig.id });
            }
        }
    };

    // Create a new part
    const createPart = async (data: PartFormData) => {
        setIsLoading(true);
        try {
            const newPartId = getNextPartId();

            // Create part using Zero mutation
            await z.mutate.parts.insert({
                id: newPartId,
                name: data.name,
                display_name: data.displayName || data.name,
                inserted_date: Date.now(),
                inserted_by: "Current User", // TODO: Get from auth context
            });

            // Insert configs if any (using the new upsert method)
            if (data.configs?.length) {
                await upsertPartConfigs(newPartId, data.configs);
            }

            toast.success(`Successfully created part "${data.name}"`);
            router.push("/parts");
            return true;
        } catch (error) {
            console.error("Error creating part:", error);
            toast.error(`Failed to create part "${data.name}"`);
            throw error;
        } finally {
            setIsLoading(false);
        }
    };

    // Update an existing part
    const updatePart = async (
        id: number,
        data: PartFormData,
        existingConfigs: part_configs[] = [],
    ) => {
        setIsLoading(true);
        try {
            // Update part
            await z.mutate.parts.update({
                id: id,
                name: data.name,
                display_name: data.displayName || data.name,
            });

            // Handle configs using the new upsert method
            await upsertPartConfigs(id, data.configs || [], existingConfigs);

            toast.success(`Successfully updated part "${data.name}"`);
            router.push("/parts");
            return true;
        } catch (error) {
            console.error("Error updating part:", error);
            toast.error(`Failed to update part "${data.name}"`);
            throw error;
        } finally {
            setIsLoading(false);
        }
    };

    // Delete a part
    const deletePart = async (part: parts) => {
        try {
            // Check if any jobs are using this part
            const { jobs: jobsUsingPart } = getJobsByPartId(part.id);
            if (jobsUsingPart && jobsUsingPart.length > 0) {
                toast.error(
                    `Failed to delete part "${part.name}". It is currently in use by ${jobsUsingPart.length} jobs.`,
                );
                return false;
            }

            // Delete the part itself
            await z.mutate.parts.delete({ id: part.id });

            toast.success(`Successfully deleted part "${part.name}"`);
            return true;
        } catch (error) {
            console.error("Error deleting part:", error);
            toast.error(`Failed to delete part "${part.name}"`);
            throw error;
        }
    };

    // Navigate to part details/edit
    const viewPartDetails = (partId: number) => {
        router.push(`/parts/${partId}/edit`);
    };

    return {
        createPart,
        updatePart,
        deletePart,
        viewPartDetails,
        isLoading,
    };
};
