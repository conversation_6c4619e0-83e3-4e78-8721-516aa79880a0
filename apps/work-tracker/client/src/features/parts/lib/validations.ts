import { z } from "zod";

// Disable when options for part configs
const DISABLE_WHEN_OPTIONS = [
    "running",
    "changeover target",
    "running or changeover target",
] as const;

// Part configuration validation schema (for part_configs table)
export const partConfigSchema = z
    .object({
        id: z.number().optional(),
        deviceType: z.string().min(1, { message: "Device type is required" }),
        deviceId: z
            .number({ required_error: "Device is required" })
            .min(1, { message: "Please select a device" }),
        idealCycleTime: z
            .number({ message: "Ideal cycle time must be a number" })
            .min(0.01, {
                message: "Ideal cycle time must be between 0.01 and 10000",
            })
            .max(10000, {
                message: "Ideal cycle time must be between 0.01 and 10000",
            }),
        taktTime: z
            .number({ message: "Takt time must be a number" })
            .min(0.01, { message: "Takt time must be between 0.01 and 10000" })
            .max(10000, {
                message: "Takt time must be between 0.01 and 10000",
            }),
        countMultiplier1: z
            .number({ message: "Count multiplier 1 must be a number" })
            .min(0, { message: "Count multiplier 1 must be at least 0" })
            .max(10000, {
                message: "Count multiplier 1 must be less than 10000",
            }),
        countMultiplier2: z
            .number({ message: "Count multiplier 2 must be a number" })
            .min(0, { message: "Count multiplier 2 must be at least 0" })
            .max(10000, {
                message: "Count multiplier 2 must be less than 10000",
            }),
        countMultiplier3: z
            .number({ message: "Count multiplier 3 must be a number" })
            .min(0, { message: "Count multiplier 3 must be at least 0" })
            .max(10000, {
                message: "Count multiplier 3 must be less than 10000",
            }),
        countMultiplier4: z
            .number({ message: "Count multiplier 4 must be a number" })
            .min(0, { message: "Count multiplier 4 must be at least 0" })
            .max(10000, {
                message: "Count multiplier 4 must be less than 10000",
            }),
        countMultiplier5: z
            .number({ message: "Count multiplier 5 must be a number" })
            .min(0, { message: "Count multiplier 5 must be at least 0" })
            .max(10000, {
                message: "Count multiplier 5 must be less than 10000",
            }),
        countMultiplier6: z
            .number({ message: "Count multiplier 6 must be a number" })
            .min(0, { message: "Count multiplier 6 must be at least 0" })
            .max(10000, {
                message: "Count multiplier 6 must be less than 10000",
            }),
        countMultiplier7: z
            .number({ message: "Count multiplier 7 must be a number" })
            .min(0, { message: "Count multiplier 7 must be at least 0" })
            .max(10000, {
                message: "Count multiplier 7 must be less than 10000",
            }),
        countMultiplier8: z
            .number({ message: "Count multiplier 8 must be a number" })
            .min(0, { message: "Count multiplier 8 must be at least 0" })
            .max(10000, {
                message: "Count multiplier 8 must be less than 10000",
            }),
        targetMultiplier: z
            .number({ message: "Target multiplier must be a number" })
            .min(0, { message: "Target multiplier must be at least 0" })
            .max(10000, {
                message: "Target multiplier must be less than 10000",
            }),
        targetLaborPerPiece: z
            .number({ message: "Target labor per piece must be a number" })
            .min(0.01, {
                message:
                    "Target labor per piece must be between 0.01 and 10000",
            })
            .max(10000, {
                message:
                    "Target labor per piece must be between 0.01 and 10000",
            }),
        downThreshold: z
            .number({ message: "Down threshold must be a number" })
            .min(0.01, {
                message: "Down threshold must be between 0.01 and 10000",
            })
            .max(10000, {
                message: "Down threshold must be between 0.01 and 10000",
            })
            .optional(),
        startWithChangeover: z.boolean(),
        changeoverTarget: z
            .number({ message: "Changeover target must be a number" })
            .min(0.01, { message: "Changeover target must be at least 0.01" })
            .max(10000, {
                message: "Changeover target must be less than 10000",
            })
            .optional(),
        changeoverReason: z.string().optional(),
        disableWhen: z.enum(DISABLE_WHEN_OPTIONS).optional(),
    })
    .refine((config) => config.idealCycleTime <= config.taktTime, {
        message: "Takt time must be greater than ideal cycle time",
        path: ["taktTime"],
    })
    .refine(
        (config) =>
            !config.startWithChangeover ||
            (config.changeoverTarget !== undefined &&
                config.changeoverTarget > 0),
        {
            message:
                "Changeover target is required when starting with changeover",
            path: ["changeoverTarget"],
        },
    )
    .refine(
        (config) =>
            !config.startWithChangeover ||
            (config.changeoverReason !== undefined &&
                config.changeoverReason.trim().length > 0),
        {
            message:
                "Changeover reason is required when starting with changeover",
            path: ["changeoverReason"],
        },
    );

// Part form validation schema based on Zero schema
export const partFormSchema = z.object({
    // Basic part information (from parts table)
    name: z
        .string({ required_error: "Part name is required" })
        .min(1, { message: "Part name is required" })
        .max(100, { message: "Part name must be less than 100 characters" }),

    displayName: z
        .string()
        .max(100, { message: "Display name must be less than 100 characters" })
        .optional(),

    // Part configurations (optional array)
    configs: z.array(partConfigSchema).optional(),
});

export type PartConfigData = z.infer<typeof partConfigSchema>;
export type PartFormData = z.infer<typeof partFormSchema>;

// Legacy schema for backward compatibility (if needed)
