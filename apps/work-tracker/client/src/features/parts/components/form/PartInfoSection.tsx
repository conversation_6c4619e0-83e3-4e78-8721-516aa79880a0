"use client";

import React from "react";
import { Control } from "react-hook-form";
import { Package } from "lucide-react";

import {
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
    FormDescription,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";

import { PartFormData } from "@/features/parts/lib/validations";

interface PartInfoSectionProps {
    control: Control<PartFormData>;
}

export function PartInfoSection({ control }: PartInfoSectionProps) {
    return (
        <Card>
            <CardHeader>
                <CardTitle className="flex items-center gap-2">
                    <Package className="h-5 w-5" />
                    Part Information
                </CardTitle>
                <CardDescription>
                    Basic details about the part including name and display
                    information.
                </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                        control={control}
                        name="name"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Part Name *</FormLabel>
                                <FormControl>
                                    <Input
                                        placeholder="Enter part name"
                                        {...field}
                                    />
                                </FormControl>
                                <FormDescription>
                                    A unique name to identify this part.
                                </FormDescription>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={control}
                        name="displayName"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Display Name</FormLabel>
                                <FormControl>
                                    <Input
                                        placeholder="Enter display name (optional)"
                                        {...field}
                                    />
                                </FormControl>
                                <FormDescription>
                                    Optional friendly name for display purposes.
                                </FormDescription>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                </div>
            </CardContent>
        </Card>
    );
}
