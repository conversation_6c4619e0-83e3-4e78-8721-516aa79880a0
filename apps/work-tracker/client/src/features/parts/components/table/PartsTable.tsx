"use client";

import { DataTable, DataTableStatus } from "@/components/ui/data-table";
import React from "react";
import getPartsColumns from "./PartsColumn";
import { parts } from "@/zero/generatedSchema";
import { useParts } from "@/hooks/useParts";

interface PartsTableProps {
    initialParts?: parts[];
    searchValue?: string;
}

export const PartsTable = ({
    initialParts = [],
    searchValue = "",
}: PartsTableProps) => {
    const { parts, status } = useParts();

    // Use initialParts for initial render, then use parts from query when available
    const displayParts =
        (parts || []).length > 0 ? parts || [] : (initialParts as parts[]);

    // Get the columns
    const columns = getPartsColumns();

    // Determine the table status
    let tableStatus: DataTableStatus = "success";
    if (status.type === "unknown") {
        tableStatus = "loading";
    } else if (displayParts.length === 0) {
        tableStatus = "empty";
    }

    return (
        <>
            <DataTable
                columns={columns as any}
                data={displayParts as any}
                status={tableStatus}
                filterValue={searchValue}
            />
        </>
    );
};

export default PartsTable;
